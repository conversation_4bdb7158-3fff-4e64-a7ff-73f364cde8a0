<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视差滚动效果测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }

        /* 测试用的前置内容 */
        .test-content {
            height: 100vh;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        /* 视差滚动测试区域 */
        .parallax-test {
            min-height: 100vh;
            background-image: url('../assets/images/bg.jpg');
            background-position: center center;
            background-size: cover;
            background-repeat: no-repeat;
            background-attachment: fixed;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .parallax-test::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1;
        }

        .parallax-content {
            position: relative;
            z-index: 2;
            text-align: center;
            color: white;
            padding: 40px;
            background: rgba(0, 100, 216, 0.25);
            border-radius: 8px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            max-width: 800px;
        }

        .parallax-content h2 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .parallax-content p {
            font-size: 1.2rem;
            line-height: 1.8;
        }

        /* 测试用的后置内容 */
        .test-content-after {
            height: 100vh;
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .parallax-test {
                background-attachment: scroll;
                min-height: 70vh;
            }

            .parallax-test::before {
                background: rgba(0, 0, 0, 0.7);
            }

            .parallax-content {
                padding: 30px 20px;
            }

            .parallax-content h2 {
                font-size: 2rem;
            }

            .parallax-content p {
                font-size: 1rem;
            }
        }

        /* 调试信息 */
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="debug-info" id="debugInfo">
        滚动位置: <span id="scrollPos">0</span>px<br>
        视口宽度: <span id="viewportWidth">0</span>px<br>
        设备类型: <span id="deviceType">未知</span>
    </div>

    <div class="test-content">
        <div>
            <h1>向下滚动测试视差效果</h1>
            <p>这是测试内容区域</p>
        </div>
    </div>

    <div class="parallax-test">
        <div class="parallax-content">
            <h2>業務佈局</h2>
            <p>以"雲鏈一體" 鍛造可信數字底座，夯築數字安全屏障可信可控；以"雲智共生" 賦能行業應用創新，推動數據要素價值加速釋放。</p>
            <br>
            <p><strong>视差效果测试</strong></p>
            <p>如果背景图固定不动，而内容可以滚动，说明视差效果正常工作。</p>
        </div>
    </div>

    <div class="test-content-after">
        <div>
            <h1>视差效果测试完成</h1>
            <p>继续滚动查看效果</p>
        </div>
    </div>

    <script>
        // 调试信息更新
        function updateDebugInfo() {
            const scrollPos = window.pageYOffset;
            const viewportWidth = window.innerWidth;
            let deviceType = '桌面端';
            
            if (viewportWidth <= 768) {
                deviceType = '移动端';
            } else if (viewportWidth <= 1024) {
                deviceType = '平板端';
            }

            document.getElementById('scrollPos').textContent = scrollPos;
            document.getElementById('viewportWidth').textContent = viewportWidth;
            document.getElementById('deviceType').textContent = deviceType;
        }

        // 监听滚动和窗口大小变化
        window.addEventListener('scroll', updateDebugInfo);
        window.addEventListener('resize', updateDebugInfo);
        
        // 初始化
        updateDebugInfo();

        // 检测背景附着支持
        function checkBackgroundAttachmentSupport() {
            const testElement = document.createElement('div');
            testElement.style.backgroundAttachment = 'fixed';
            const isSupported = testElement.style.backgroundAttachment === 'fixed';
            
            console.log('background-attachment: fixed 支持:', isSupported);
            
            if (!isSupported) {
                console.warn('当前浏览器不支持 background-attachment: fixed');
            }
        }

        checkBackgroundAttachmentSupport();
    </script>
</body>
</html>
