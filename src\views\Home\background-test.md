# 业务布局背景图设置验证

## 背景图配置

### 文件路径
- 背景图文件：`src/assets/images/bg.jpg`
- CSS引用路径：`@/assets/images/bg.jpg`

### CSS设置
```css
.business-layout {
  padding: 60px 0;
  background: url('@/assets/images/bg.jpg') center/cover;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

.business-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.8) 0%, rgba(22, 33, 62, 0.8) 50%, rgba(15, 52, 96, 0.8) 100%);
  z-index: 1;
}
```

### 层级结构
1. **背景图层** (z-index: auto) - 背景图片
2. **遮罩层** (z-index: 1) - 半透明渐变遮罩，确保文字可读性
3. **内容层** (z-index: 2) - 所有文字和业务卡片内容
4. **装饰层** (z-index: 1) - 装饰元素，与遮罩层同级

### 特性
- **响应式**: 背景图会根据容器大小自动调整
- **覆盖模式**: 使用 `cover` 确保背景图完全覆盖容器
- **居中定位**: 背景图始终居中显示
- **遮罩效果**: 添加渐变遮罩确保文字清晰可读
- **性能优化**: 使用 CSS 而非 JavaScript 处理背景

### 验证清单
- [x] 背景图文件存在于正确路径
- [x] CSS 路径别名 `@/` 配置正确
- [x] 背景图设置为覆盖模式
- [x] 添加半透明遮罩层
- [x] 内容层 z-index 设置正确
- [x] 响应式设计兼容

### 浏览器兼容性
- Chrome: ✅ 支持
- Firefox: ✅ 支持  
- Safari: ✅ 支持
- Edge: ✅ 支持

### 注意事项
1. 确保 `bg.jpg` 文件大小适中，建议压缩优化
2. 如果背景图过亮或过暗，可以调整遮罩层的透明度
3. 在不同设备上测试背景图的显示效果
