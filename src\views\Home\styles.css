/* Home 页面样式 */
.home-page {
  min-height: 100vh;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
/* 公司简介模块 */
.company-intro {
  padding: 60px 0;
  background: url('/uploads/sites/1012/2022/11/8c42f6797950f4aa3e1d8eb632b7ddb5.jpg') center/cover;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;
}

/* 第一行：公司简介内容 */
.intro-content-row {
  display: flex;
  gap: 50px;
  align-items: flex-start;
  align-items: center;
  margin-bottom: 50px;
  position: relative;
  z-index: 2;
}

/* 左侧公司Logo (9/24 = 37.5%) */
.intro-left {
  flex: 0 0 37.5%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.company-logo img {
  max-width: 500px;
  height: auto;
}

/* 右侧公司简介文本 (15/24 = 62.5%) */
.intro-right {
  flex: 0 0 62.5%;
  padding-left: 50px;
}

.intro-text-content {
  /* padding: 30px; */
}

.intro-subtitle {
  color: #0064d8;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 15px 0;
  line-height: 1.5;
  text-align: justify;
}

.intro-title {
  color: #333;
  font-size: 34px;
  font-weight: bold;
  margin: 0 0 15px 0;
  line-height: 1.5;
  text-align: justify;
}

.intro-divider {
  margin: 15px 0;
  text-align: left;
}

.intro-divider img {
  display: block;
}

.intro-description {
  margin-bottom: 20px;
}

.intro-text {
  color: #333;
  font-size: 16px;
  line-height: 2;
  margin: 0;
  text-align: justify;
  text-justify: inter-ideograph;
}

.view-more-section {
  margin-top: 30px;
}

.view-more-btn {
  background: rgba(0, 129, 249, 1);
  color: #ffffff;
  border: 1px solid #ccc;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.view-more-btn:hover {
  background: rgba(0, 100, 216, 1);
  color: #ffffff;
}

/* 第二行：统计数据卡片 */
.statistics-row {
  position: relative;
  z-index: 2;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

/* 统计卡片样式 */
.stat-card {
  background: #ffffff;
  padding: 50px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: left;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right top;
  opacity: 0.3;
  z-index: 1;
}

.stat-card:nth-child(1)::before {
  background-image: url('/uploads/sites/1012/2022/11/79787277c39e51d6ed1a504b977c2032.png');
}

.stat-card:nth-child(2)::before {
  background-image: url('/uploads/sites/1012/2022/11/2f3e9140557119d54fbef7c2b625efa7.png');
}

.stat-card:nth-child(3)::before {
  background-image: url('/uploads/sites/1012/2022/11/1171bf6762c3ee5dd7c158f85ff103a9.png');
}

.stat-card .stat-number {
  display: flex;
  align-items: baseline;
  justify-content: flex-start;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
  transform: translateY(-20px);
}

.stat-card .counter {
  font-size: 60px;
  font-weight: bold;
  color: #0064d8;
  line-height: 1;
}

.stat-card .plus {
  font-size: 60px;
  font-weight: bold;
  color: #0064d8;
  margin-left: 5px;
}

.stat-card h3 {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 15px 0;
  position: relative;
  z-index: 2;
  transform: translateY(-55px);
}

.stat-card p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  position: relative;
  z-index: 2;
  text-align: justify;
  text-justify: inter-ideograph;
  transform: translateY(-40px);
}

/* 悬停动画效果 */
.hvr-float {
  transition: transform 0.3s ease;
}

.hvr-float:hover {
  transform: translateY(-10px);
}

.custom-yy {
  transition: all 0.3s ease;
}

.custom-yy:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}





/* 智慧生态模块 */
.smart-ecology {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  color: #fff;
  font-size: 32px;
  margin: 0 0 10px 0;
}

.section-header .section-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

.ecology-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.ecology-item {
  text-align: center;
  padding: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  transition: all 0.3s;
  cursor: pointer;
}

.ecology-item:hover {
  background: rgba(0, 100, 216, 1);
  transform: translateY(-5px);
}

.item-icon {
  margin-bottom: 20px;
}

.item-icon img {
  width: 60px;
  height: 60px;
}

.ecology-item h3 {
  color: #fff;
  font-size: 18px;
  margin: 0 0 10px 0;
}

.ecology-item p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

/* 人工智能模块 */
.ai-section {
  padding: 80px 0;
  background: rgba(0, 100, 216, 0.31);
}

.ai-section .section-header h2 {
  color: #fff;
}

.ai-category h3 {
  color: #fff;
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
}

.ai-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 600px;
  margin: 0 auto;
}

.ai-item {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  transition: all 0.3s;
  cursor: pointer;
}

.ai-item:hover {
  background: rgba(0, 100, 216, 1);
  transform: translateY(-5px);
}

.ai-item img {
  width: 50px;
  height: 50px;
  margin-bottom: 15px;
}

.ai-item span {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  display: block;
}

.ai-item p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin: 0;
  line-height: 1.3;
}

/* 数据统计模块 */
.statistics-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin: 0 auto;
}

.stat-card {
  background: #fff;
  padding: 50px;
  border-radius: 10px;
  text-align: left;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background: url('/uploads/sites/1012/2022/11/79787277c39e51d6ed1a504b977c2032.png') no-repeat;
  background-size: contain;
  opacity: 0.1;
}

.stat-number {
  display: flex;
  align-items: baseline;
  margin-bottom: 20px;
}

.counter {
  font-size: 60px;
  font-weight: bold;
  color: #0064d8;
  line-height: 1;
}

.plus {
  font-size: 24px;
  font-weight: bold;
  color: #0064d8;
  margin-left: 5px;
}

.stat-card h3 {
  color: #333;
  font-size: 18px;
  margin-bottom: 15px;
  font-weight: bold;
}

.stat-card p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* 新闻中心 */
.news-center {
  padding: 80px 0;
  background: #fff;
}

.section-subtitle {
  color: #333;
  font-size: 14px;
  text-align: center;
  margin: 0 0 10px 0;
}

.news-center .section-header h2 {
  color: #333;
  text-align: center;
}

.news-list {
  max-width: 800px;
  margin: 0 auto 40px;
}

.news-item {
  display: flex;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.news-item:hover {
  background: rgba(245, 246, 251, 1);
  transform: translateY(-2px);
}

.news-image {
  width: 300px;
  height: 225px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-content {
  flex: 1;
  padding: 30px;
  display: flex;
  align-items: center;
}

.news-content h3 {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.view-more-btn {
  display: block;
  margin: 0 auto;
  padding: 12px 24px;
  background: transparent;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.view-more-btn:hover {
  color: #0064d8;
  border-color: #0064d8;
}



/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.pulse-hover:hover {
  animation: pulse 0.3s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .company-intro {
    padding: 40px 0;
  }

  .intro-content-row {
    flex-direction: column;
    gap: 30px;
    margin-bottom: 30px;
  }

  .intro-left {
    flex: none;
    width: 100%;
  }

  .intro-right {
    flex: none;
    width: 100%;
    padding-left: 0;
  }

  .intro-text-content {
    padding: 20px;
  }

  .intro-title {
    font-size: 20px;
  }

  .intro-text {
    font-size: 14px;
    line-height: 1.8;
  }

  .statistics-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stat-card {
    padding: 30px 20px;
  }

  .stat-card .counter {
    font-size: 48px;
  }

  .stat-card .plus {
    font-size: 48px;
  }

  .stat-card h3 {
    font-size: 16px;
  }

  .stat-card p {
    font-size: 13px;
  }

  .ecology-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .ai-grid {
    grid-template-columns: 1fr;
  }

  .news-item {
    flex-direction: column;
  }

  .news-image {
    width: 100%;
    height: 200px;
  }
}

/* 业务布局模块样式 - 简化的视差滚动效果 */
.business-layout {
  /* 基础布局 */
  padding: 80px 0;
  position: relative;
  overflow: hidden;
  min-height: 100vh;

  /* 视差背景核心设置 */
  background-image: url('@/assets/images/bg.jpg');
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;

  /* 确保在所有浏览器中正常工作 */
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
}

/* 深色遮罩层确保文字可读性 */
.business-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1;
  pointer-events: none;
}

/* 响应式处理 */
@media (max-width: 768px) {
  .business-layout {
    /* 移动端禁用固定背景，因为iOS Safari支持不好 */
    background-attachment: scroll;
    min-height: 70vh;
    padding: 50px 0;
  }

  .business-layout::before {
    /* 移动端加深遮罩确保可读性 */
    background: rgba(0, 0, 0, 0.7);
  }
}

/* 桌面端和平板端保持视差效果 */
@media (min-width: 769px) {
  .business-layout {
    background-attachment: fixed;
    min-height: 100vh;
  }
}

/* 标题区域 */
.business-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
  padding: 0 20px;
}

.business-subtitle {
  color: #ffffff;
  font-size: 16px;
  margin-bottom: 10px;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.business-title {
  color: #ffffff;
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 20px;
  line-height: 1.2;
}

.business-description {
  color: #ffffff;
  font-size: 16px;
  line-height: 1.6;
  max-width: 900px;
  margin: 0 auto;
  text-align: justify;
}

@media (max-width: 767px) {
  .business-title {
    font-size: 32px;
  }

  .business-description {
    font-size: 14px;
    padding: 0 20px;
  }
}

/* ========================================
   行業雲模块布局结构重构
   ======================================== */

/* 业务板块容器 */
.business-sections {
  display: flex;
  flex-direction: column;
  gap: 40px;
  position: relative;
  z-index: 2;
}

/* ========================================
   第一层：业务板块整体容器
   ======================================== */
.business-section {
  width: 100%;
  /* 整体板块采用上下两层结构 */
  display: flex;
  flex-direction: column;
}

/* ========================================
   第二层上部：标题区域 (section-header)
   ======================================== */
.section-header {
  /* 标题区域布局 */
  text-align: center;
  margin-bottom: 30px; /* 与下方内容区域的间距 */

  /* 标题区域内部采用垂直布局 */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px; /* 标题与分割线间距 */
}

/* 主标题样式 */
.section-title {
  /* 蓝色半透明背景块 */
  background-color: rgba(0, 100, 216, 0.31);
  color: #ffffff;
  font-size: 24px;
  font-weight: bold;

  /* 圆角矩形容器 */
  padding: 15px 30px;
  border-radius: 4px;

  /* 水平居中显示 */
  margin: 0;
  display: inline-block;
}

/* 标题下方分割线 */
.section-divider {
  width: 100%;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 0;
}

/* ========================================
   第二层下部：内容展示区域 (business-carousel)
   ======================================== */
.business-carousel {
  width: 100%;
  /* 内容区域布局 */
}

/* 内容区域居中容器 */
.carousel-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* ========================================
   第三层：主内容卡片 (business-card)
   ======================================== */
.business-card {
  /* 卡片整体样式 */
  background-color: rgba(0, 100, 216, 0.25);
  border-radius: 8px;
  position: relative;
  overflow: hidden;

  /* 卡片尺寸和居中 */
  max-width: 1000px;
  width: 100%;
  padding: 40px;

  /* 毛玻璃效果和边框 */
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);

  /* 卡片内部采用上下两个子区域布局 */
  display: flex;
  flex-direction: column;
}

/* 卡片背景装饰 */
.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/uploads/sites/1012/2022/11/892e009941da97e452b95f6ee3785e64.png');
  background-position: center bottom;
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0.1;
  z-index: 1;
}

/* 卡片内容容器 */
.card-content {
  position: relative;
  z-index: 2;

  /* 内容区域采用上下布局 */
  display: flex;
  flex-direction: column;
}

/* ========================================
   第四层上部：卡片标题区域 (card-title)
   ======================================== */
.card-title {
  color: #ffffff;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30px; /* 与下方功能项目区域的间距 */
}

/* ========================================
   第四层下部：功能项目展示区域 (card-items)
   ======================================== */
.card-items {
  /* CSS Grid网格布局 */
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 3列等宽布局 */
  gap: 20px; /* 列间距 */
  justify-items: center; /* 项目居中对齐 */
}

/* 移动端响应式：改为单列布局 */
@media (max-width: 767px) {
  .card-items {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

/* ========================================
   第五层：功能项目子模块 (card-item)
   ======================================== */

/* 单个功能项目容器 */
.card-item {
  /* 项目容器基础样式 */
  display: flex;
  flex-direction: column; /* 垂直排列：图标在上，文字在下 */
  align-items: center;

  /* 项目容器尺寸 */
  padding: 20px;
  min-height: 120px;
  width: 100%;
  max-width: 200px; /* 桌面端最大宽度 */

  /* 项目容器外观 */
  border-radius: 8px;
  background-image: url('/uploads/sites/1012/2022/11/2cc8948b47bc332aad648adc9c6f443a.png');
  background-position: right center;
  background-repeat: no-repeat;
  background-size: contain;

  /* 交互效果 */
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 悬停扫描动画效果 */
.card-item.hvr-sweep-to-top {
  position: relative;
  overflow: hidden;
}

.card-item.hvr-sweep-to-top::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 100, 216, 1);
  transition: top 0.3s ease;
  z-index: -1;
}

.card-item.hvr-sweep-to-top:hover::before {
  top: 0;
}

/* 项目悬停效果 */
.card-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 100, 216, 0.3);
}

.card-item:hover * {
  color: #ffffff !important;
}

/* ========================================
   第六层：项目内部元素布局
   ======================================== */

/* 图标区域（项目上部） */
.item-icon {
  /* 图标区域占项目宽度的36% */
  width: 36%;

  /* 图标居中显示 */
  display: flex;
  justify-content: center;
  align-items: center;

  /* 与下方文字区域的间距 */
  margin-bottom: 10px;
}

/* 图标图片样式 */
.item-icon img {
  max-width: 40px;
  height: auto;
}

/* 文字区域（项目下部） */
.item-text {
  /* 文字区域占项目宽度的64% */
  width: calc(100% - 36%);

  /* 文字样式 */
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  line-height: 1;

  /* 文字居中对齐 */
  text-align: center;
}

/* 装饰元素 */
.business-decoration {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 1;
  pointer-events: none;
}

.decoration-left {
  position: absolute;
  left: 7%;
  top: 50%;
  transform: translateY(-50%) translateX(12px);
  max-width: 30px;
  height: auto;
  opacity: 0.6;
}

.decoration-right {
  position: absolute;
  right: 7%;
  top: 50%;
  transform: translateY(-50%) translateX(-12px) scaleX(-1);
  max-width: 30px;
  height: auto;
  opacity: 0.6;
}

/* ========================================
   响应式设计 - 按布局层次组织
   ======================================== */

/* 移动端 (≤767px) */
@media (max-width: 767px) {
  /* 第二层：标题区域响应式 */
  .section-header {
    margin-bottom: 20px; /* 减少与内容区域的间距 */
  }

  .section-title {
    font-size: 20px; /* 减小标题字体 */
    padding: 12px 24px; /* 减小内边距 */
  }

  /* 第三层：主内容卡片响应式 */
  .business-card {
    padding: 20px; /* 减小卡片内边距 */
    margin: 0 20px; /* 添加左右边距 */
  }

  /* 第四层上部：卡片标题响应式 */
  .card-title {
    font-size: 22px; /* 减小字体 */
    margin-bottom: 20px; /* 减少与功能项目的间距 */
  }

  /* 第五层：功能项目响应式 */
  .card-item {
    min-height: 100px; /* 减小最小高度 */
    padding: 15px; /* 减小内边距 */
  }

  /* 第六层：项目内部元素响应式 */
  .item-icon img {
    max-width: 30px; /* 减小图标尺寸 */
  }

  .item-text {
    font-size: 12px; /* 减小文字大小 */
  }

  /* 装饰元素在移动端隐藏 */
  .decoration-left,
  .decoration-right {
    display: none;
  }
}

/* 平板端 (768px-991px) */
@media (min-width: 768px) {
  /* 第一层：业务板块容器 */
  .business-sections {
    gap: 50px; /* 增加板块间距 */
  }

  /* 第二层：标题区域 */
  .section-title {
    font-size: 28px; /* 增大标题字体 */
    padding: 20px 40px; /* 增大内边距 */
  }

  /* 第三层：主内容卡片 */
  .business-card {
    padding: 40px; /* 标准内边距 */
  }

  /* 第四层上部：卡片标题 */
  .card-title {
    font-size: 32px; /* 增大字体 */
    margin-bottom: 40px; /* 增加与功能项目的间距 */
  }
}

/* 桌面端 (992px-1199px) */
@media (min-width: 992px) {
  /* 第三层：主内容卡片 */
  .business-card {
    padding: 50px; /* 增大内边距 */
  }

  /* 第四层下部：功能项目网格 */
  .card-items {
    gap: 30px; /* 增大网格间距 */
  }

  /* 第五层：功能项目 */
  .card-item {
    max-width: 250px; /* 增大最大宽度 */
    min-height: 140px; /* 增大最小高度 */
    padding: 25px; /* 增大内边距 */
  }

  /* 第六层：项目内部元素 */
  .item-icon img {
    max-width: 50px; /* 增大图标尺寸 */
  }

  .item-text {
    font-size: 16px; /* 增大文字大小 */
  }
}

/* 大屏桌面端 (≥1200px) */
@media (min-width: 1200px) {
  /* 第三层：主内容卡片 */
  .business-card {
    padding: 60px; /* 最大内边距 */
  }

  /* 装饰元素定位调整 */
  .decoration-left {
    transform: translateY(-50%) translateX(16px);
  }

  .decoration-right {
    transform: translateY(-50%) translateX(-16px) scaleX(-1);
  }
}

@media (min-width: 1360px) {
  .decoration-left {
    transform: translateY(-50%) translateX(20px);
  }

  .decoration-right {
    transform: translateY(-50%) translateX(-20px) scaleX(-1);
  }
}

@media (min-width: 1600px) {
  .decoration-left {
    transform: translateY(-50%) translateX(23px);
  }

  .decoration-right {
    transform: translateY(-50%) translateX(-23px) scaleX(-1);
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 视差滚动专用动画 */
@keyframes parallaxFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes backgroundShift {
  0% {
    background-position: center 0%;
  }
  100% {
    background-position: center 100%;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 业务卡片进入动画 */
.business-card {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

.business-card.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* 卡片项目悬停效果增强 */
.card-item {
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 100, 216, 0.4);
}

/* 标题动画 */
.business-header {
  animation: fadeInUp 1s ease 0.2s both;
}

.section-title {
  transition: all 0.3s ease;
}

.section-title:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 100, 216, 0.3);
}
