<template>
  <div class="slider-test">
    <h1>滑块测试页面</h1>
    <div class="slider-container">
      <input 
        type="range" 
        v-model="sliderValue" 
        min="0" 
        max="100" 
        class="slider"
      />
      <div class="value-display">
        当前值: {{ sliderValue }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'SliderTest',
  setup() {
    const sliderValue = ref(50)
    
    return {
      sliderValue
    }
  }
}
</script>

<style scoped>
.slider-test {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.slider-container {
  margin-top: 30px;
  text-align: center;
}

.slider {
  width: 100%;
  height: 10px;
  border-radius: 5px;
  background: #ddd;
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.slider:hover {
  opacity: 1;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4CAF50;
  cursor: pointer;
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4CAF50;
  cursor: pointer;
  border: none;
}

.value-display {
  margin-top: 20px;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
</style>
