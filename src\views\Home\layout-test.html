<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行業雲模块布局测试</title>
    <style>
        /* 引入重构后的样式 */
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #1a1a2e;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* 复制重构后的核心样式 */
        .business-section {
            width: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .section-title {
            background-color: rgba(0, 100, 216, 0.31);
            color: #ffffff;
            font-size: 24px;
            font-weight: bold;
            padding: 15px 30px;
            border-radius: 4px;
            margin: 0;
            display: inline-block;
        }
        
        .section-divider {
            width: 100%;
            height: 1px;
            background-color: rgba(255, 255, 255, 0.1);
            margin: 0;
        }
        
        .business-carousel {
            width: 100%;
        }
        
        .carousel-container {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .business-card {
            background-color: rgba(0, 100, 216, 0.25);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
            padding: 40px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }
        
        .card-content {
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
        }
        
        .card-title {
            color: #ffffff;
            font-size: 28px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .card-items {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            justify-items: center;
        }
        
        .card-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            min-height: 120px;
            width: 100%;
            max-width: 200px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .card-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 100, 216, 0.3);
            background: rgba(0, 100, 216, 1);
        }
        
        .item-icon {
            width: 36%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .item-icon img {
            max-width: 40px;
            height: auto;
            background: white;
            border-radius: 50%;
            padding: 8px;
        }
        
        .item-text {
            width: calc(100% - 36%);
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
            line-height: 1;
            text-align: center;
        }
        
        /* 响应式 */
        @media (max-width: 767px) {
            .card-items {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .section-title {
                font-size: 20px;
                padding: 12px 24px;
            }
            
            .business-card {
                padding: 20px;
                margin: 0 20px;
            }
            
            .card-title {
                font-size: 22px;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 40px;">行業雲模块布局测试</h1>
        
        <!-- 行业云板块 -->
        <div class="business-section industry-cloud">
            <!-- 第二层上部：标题区域 -->
            <div class="section-header">
                <h3 class="section-title">行業雲</h3>
                <div class="section-divider"></div>
            </div>

            <!-- 第二层下部：内容展示区域 -->
            <div class="business-carousel">
                <div class="carousel-container">
                    <!-- 第三层：主内容卡片 -->
                    <div class="business-card">
                        <div class="card-content">
                            <!-- 第四层上部：卡片标题区域 -->
                            <h4 class="card-title">智慧文旅</h4>
                            
                            <!-- 第四层下部：功能项目展示区域 -->
                            <div class="card-items">
                                <!-- 第五层：功能项目子模块 -->
                                <div class="card-item">
                                    <!-- 第六层：项目内部元素 -->
                                    <div class="item-icon">
                                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjMDA2NGQ4Ii8+Cjwvc3ZnPgo=" alt="數字景區" />
                                    </div>
                                    <div class="item-text">數字景區</div>
                                </div>
                                
                                <div class="card-item">
                                    <div class="item-icon">
                                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjMDA2NGQ4Ii8+Cjwvc3ZnPgo=" alt="智能營銷" />
                                    </div>
                                    <div class="item-text">智能營銷</div>
                                </div>
                                
                                <div class="card-item">
                                    <div class="item-icon">
                                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjMDA2NGQ4Ii8+Cjwvc3ZnPgo=" alt="智慧監管" />
                                    </div>
                                    <div class="item-text">智慧監管</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 40px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 8px;">
            <h3>布局层次说明：</h3>
            <ul style="line-height: 1.8;">
                <li><strong>第一层</strong>：业务板块整体容器 (.business-section) - 上下两层结构</li>
                <li><strong>第二层上部</strong>：标题区域 (.section-header) - 包含"行業雲"标题和分割线</li>
                <li><strong>第二层下部</strong>：内容展示区域 (.business-carousel) - 居中容器</li>
                <li><strong>第三层</strong>：主内容卡片 (.business-card) - 半透明蓝色卡片，上下两个子区域</li>
                <li><strong>第四层上部</strong>：卡片标题区域 (.card-title) - "智慧文旅"标题</li>
                <li><strong>第四层下部</strong>：功能项目展示区域 (.card-items) - 3列网格布局</li>
                <li><strong>第五层</strong>：功能项目子模块 (.card-item) - 单个功能项目容器</li>
                <li><strong>第六层</strong>：项目内部元素 (.item-icon + .item-text) - 图标和文字</li>
            </ul>
        </div>
    </div>
</body>
</html>
