# 视差滚动效果测试指南

## 🎯 视差滚动实现概述

### 核心技术
- **CSS**: `background-attachment: fixed` 
- **JavaScript**: 移动端兼容性处理和增强效果
- **响应式设计**: 不同设备的适配策略

### 实现效果
1. **桌面端**: 完整的视差滚动效果，背景图固定在视口
2. **平板端**: 优化的视差效果，适中的视觉层次
3. **移动端**: 替代方案，使用 JavaScript 模拟视差效果

## 🔧 技术实现细节

### CSS 核心设置
```css
.business-layout {
  background: url('@/assets/images/bg.jpg') center center;
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;  /* 关键属性 */
  min-height: 100vh;
  position: relative;
}
```

### JavaScript 增强
```javascript
// 移动端视差替代方案
const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    const ratio = entry.intersectionRatio
    const yPos = -(1 - ratio) * 50
    element.style.backgroundPosition = `center ${yPos}%`
  })
}, {
  threshold: Array.from({length: 101}, (_, i) => i / 100)
})
```

## 📱 设备兼容性

### 桌面端 (≥1025px)
- ✅ 完整视差效果
- ✅ `background-attachment: fixed`
- ✅ 滚动监听增强
- ✅ 100vh 最小高度

### 平板端 (768px-1024px)
- ✅ 标准视差效果
- ✅ `background-attachment: fixed`
- ✅ 90vh 最小高度
- ✅ 优化的性能设置

### 移动端 (≤767px)
- ⚠️ `background-attachment: scroll`
- ✅ JavaScript 模拟视差
- ✅ Intersection Observer
- ✅ 80vh 最小高度
- ✅ 性能优化

## 🎨 视觉层次

### 层级结构
1. **背景层** (z-index: auto)
   - 固定背景图片
   - 视差滚动效果

2. **遮罩层** (z-index: 1)
   - 半透明渐变遮罩
   - 确保内容可读性

3. **内容层** (z-index: 2)
   - 标题区域
   - 业务卡片
   - 交互元素

### 玻璃态效果
- `backdrop-filter: blur()`
- 半透明背景
- 边框高光
- 阴影层次

## 🧪 测试清单

### 功能测试
- [ ] 桌面端视差滚动正常
- [ ] 移动端替代效果工作
- [ ] 背景图正确加载
- [ ] 遮罩层显示正常
- [ ] 内容层级正确

### 性能测试
- [ ] 滚动流畅度
- [ ] 移动端性能
- [ ] 内存使用情况
- [ ] 电池消耗影响

### 兼容性测试
- [ ] Chrome/Edge
- [ ] Firefox
- [ ] Safari
- [ ] 移动端浏览器

### 视觉测试
- [ ] 背景图居中显示
- [ ] 文字清晰可读
- [ ] 层次感明显
- [ ] 动画效果流畅

## 🚀 优化建议

### 性能优化
1. **图片优化**: 压缩背景图片大小
2. **懒加载**: 考虑背景图懒加载
3. **GPU加速**: 使用 `transform3d(0,0,0)`
4. **节流处理**: 滚动事件节流

### 用户体验
1. **加载状态**: 添加背景图加载指示
2. **降级方案**: 网络慢时的备用方案
3. **可访问性**: 考虑动画敏感用户
4. **电池优化**: 移动端省电模式

## 🐛 常见问题

### iOS Safari 问题
- `background-attachment: fixed` 支持有限
- 使用 JavaScript 替代方案
- 避免在滚动时频繁重绘

### Android 浏览器
- 部分版本性能问题
- 使用 `will-change` 属性
- 优化动画性能

### 性能问题
- 避免复杂的 backdrop-filter
- 使用 CSS 硬件加速
- 合理设置动画帧率
