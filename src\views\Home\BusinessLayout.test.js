/**
 * 业务布局模块测试文件
 * 用于验证重构后的业务布局功能
 */

// 模拟测试业务布局模块的基本功能
describe('业务布局模块测试', () => {
  
  // 测试业务数据结构
  test('业务板块数据结构正确', () => {
    const businessSections = [
      {
        id: 'industry-cloud',
        title: '行業雲',
        items: [
          {
            name: '智慧文旅',
            subItems: [
              { name: '數字景區', icon: '/uploads/sites/1012/2022/11/22093492e9cda93d64e9df82cca6706e.png' },
              { name: '智能營銷', icon: '/uploads/sites/1012/2022/11/cca01f8608a48e4299e118c4f86a71fb.png' },
              { name: '智慧生態', icon: '/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png' }
            ]
          }
        ]
      },
      {
        id: 'smart-ecology',
        title: '智慧生態',
        items: [
          {
            name: '智慧生態',
            subItems: [
              { name: '生態監測', icon: '/uploads/sites/1012/2022/11/073346750cbf5e05e3ea59df9549578a.png' },
              { name: '災害預警', icon: '/uploads/sites/1012/2022/11/d1fba75503642fc884e617bc2a146ff9.png' },
              { name: '森林防火', icon: '/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png' }
            ]
          }
        ]
      },
      {
        id: 'artificial-intelligence',
        title: '人工智能',
        items: [
          {
            name: '垂類大模型',
            subItems: [
              { name: '智能分析', icon: '/uploads/sites/1012/2024/07/83841dc18fcc7f97315235a2ac571d46.png' },
              { name: '智能決策', icon: '/uploads/sites/1012/2022/11/cca01f8608a48e4299e118c4f86a71fb.png' },
              { name: '智能應用', icon: '/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png' }
            ]
          }
        ]
      }
    ]

    expect(businessSections).toHaveLength(3)
    expect(businessSections[0].title).toBe('行業雲')
    expect(businessSections[1].title).toBe('智慧生態')
    expect(businessSections[2].title).toBe('人工智能')
  })

  // 测试CSS类名是否正确
  test('CSS类名结构正确', () => {
    const expectedClasses = [
      'business-layout',
      'business-header',
      'business-title',
      'business-subtitle',
      'business-description',
      'business-sections',
      'business-section',
      'section-header',
      'section-title',
      'section-divider',
      'business-carousel',
      'carousel-container',
      'business-card',
      'card-background',
      'card-content',
      'card-title',
      'card-items',
      'card-item',
      'item-icon',
      'item-text',
      'business-decoration',
      'decoration-left',
      'decoration-right'
    ]

    // 这里可以添加实际的DOM测试逻辑
    expect(expectedClasses).toContain('business-layout')
    expect(expectedClasses).toContain('business-card')
    expect(expectedClasses).toContain('card-item')
  })

  // 测试响应式断点
  test('响应式断点设置正确', () => {
    const breakpoints = {
      mobile: 767,
      tablet: 768,
      desktop: 992,
      large: 1200,
      xlarge: 1360,
      xxlarge: 1600
    }

    expect(breakpoints.mobile).toBe(767)
    expect(breakpoints.tablet).toBe(768)
    expect(breakpoints.desktop).toBe(992)
  })

  // 测试动画类名
  test('动画类名正确', () => {
    const animationClasses = [
      'hvr-sweep-to-top',
      'animate-in',
      'fadeInUp',
      'slideInLeft',
      'slideInRight'
    ]

    expect(animationClasses).toContain('hvr-sweep-to-top')
    expect(animationClasses).toContain('animate-in')
  })
})

// 导出测试配置
export default {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  }
}
